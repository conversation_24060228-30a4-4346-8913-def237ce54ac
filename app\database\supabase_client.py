#!/usr/bin/env python3
"""
Supabase client for the Cold Email Server
"""
import os
import json
import datetime
from typing import List, Dict, Any, Optional, Union
import supabase
from app.utils.logger import setup_logger

# Set up logger
logger = setup_logger('database.supabase')

class SupabaseClient:
    """Client for interacting with Supabase database."""

    def __init__(self, url: str = None, key: str = None):
        """
        Initialize the Supabase client.

        Args:
            url (str, optional): Supabase URL. Defaults to environment variable.
            key (str, optional): Supabase API key. Defaults to environment variable.
        """
        self.url = url or os.environ.get('SUPABASE_URL')
        self.key = key or os.environ.get('SUPABASE_KEY')

        if not self.url or not self.key:
            logger.error("Supabase URL or key not provided. Cannot continue without database connection.")
            raise ValueError("Supabase URL and key are required. Please set them in the .env file.")

        logger.info(f"Initializing Supabase client with URL: {self.url}")
        self.client = supabase.create_client(self.url, self.key)

    def is_connected(self) -> bool:
        """
        Check if the client is connected to Supabase.

        Note: This method always returns True now that we've removed mock mode.
        It's kept for backward compatibility.

        Returns:
            bool: True if connected, False otherwise
        """
        return True  # Always returns True now that we've removed mock mode

    def _handle_response(self, response):
        """Handle Supabase response and log errors."""
        if hasattr(response, 'error') and response.error:
            logger.error(f"Supabase error: {response.error}")
            return None
        return response.data

    # Email Accounts

    def get_email_accounts(self, active_only: bool = True) -> List[Dict[str, Any]]:
        """
        Get all email accounts.

        Args:
            active_only (bool): Only return active accounts

        Returns:
            List[Dict[str, Any]]: List of email accounts
        """
        query = self.client.table('email_accounts')
        if active_only:
            query = query.eq('active', True)

        response = query.select('*').execute()
        return self._handle_response(response)

    def create_email_account(self, email: str, display_name: str = None,
                            smtp_server: str = 'smtp.zoho.eu', smtp_port: int = 465,
                            use_ssl: bool = True, daily_limit: int = 10) -> Dict[str, Any]:
        """
        Create a new email account.

        Args:
            email (str): Email address
            display_name (str, optional): Display name
            smtp_server (str, optional): SMTP server
            smtp_port (int, optional): SMTP port
            use_ssl (bool, optional): Use SSL
            daily_limit (int, optional): Daily email limit

        Returns:
            Dict[str, Any]: Created account
        """
        account_data = {
            'email': email,
            'display_name': display_name or email.split('@')[0],
            'smtp_server': smtp_server,
            'smtp_port': smtp_port,
            'use_ssl': use_ssl,
            'daily_limit': daily_limit,
            'active': True
        }

        response = self.client.table('email_accounts').insert(account_data).execute()
        return self._handle_response(response)

    def get_email_account_by_id(self, account_id: str) -> Dict[str, Any]:
        """
        Get an email account by its ID.

        Args:
            account_id (str): Account ID

        Returns:
            Dict[str, Any]: Account data or None if not found
        """
        try:
            response = self.client.table('email_accounts').select('*').eq('id', account_id).execute()
            result = self._handle_response(response)

            if result and len(result) > 0:
                return result[0]
            else:
                return None

        except Exception as e:
            logger.error(f"Error getting email account by ID {account_id}: {str(e)}")
            return None

    # Recipients

    def get_recipients(self, status: str = 'active') -> List[Dict[str, Any]]:
        """
        Get all recipients.

        Args:
            status (str, optional): Filter by status

        Returns:
            List[Dict[str, Any]]: List of recipients
        """
        # Build the query
        query = self.client.table('recipients').select('*')
        if status:
            query = query.eq('status', status)

        response = query.execute()
        return self._handle_response(response)

    def create_recipient(self, email: str, first_name: str = None,
                        last_name: str = None, company: str = None) -> Dict[str, Any]:
        """
        Create a new recipient.

        Args:
            email (str): Email address
            first_name (str, optional): First name
            last_name (str, optional): Last name
            company (str, optional): Company name

        Returns:
            Dict[str, Any]: Created recipient
        """
        recipient_data = {
            'email': email,
            'first_name': first_name,
            'last_name': last_name,
            'company': company,
            'status': 'active'
        }

        response = self.client.table('recipients').insert(recipient_data).execute()
        return self._handle_response(response)

    # Email Queue

    def queue_email(self, account_id: str, recipient_id: str, subject: str,
                   body: str, scheduled_time: datetime.datetime) -> Dict[str, Any]:
        """
        Add an email to the queue.

        Args:
            account_id (str): Email account ID
            recipient_id (str): Recipient ID
            subject (str): Email subject
            body (str): Email body
            scheduled_time (datetime.datetime): Scheduled send time

        Returns:
            Dict[str, Any]: Queued email
        """
        # First, check if we already have this content in the email_content table
        response = self.client.table('email_content').select('id').eq('subject', subject).eq('body', body).execute()
        content_result = self._handle_response(response)

        content_id = None
        if content_result and len(content_result) > 0:
            # Use existing content
            content_id = content_result[0]['id']
            logger.debug(f"Using existing email content with ID: {content_id}")
        else:
            # Create new content
            content_data = {
                'subject': subject,
                'body': body
            }
            response = self.client.table('email_content').insert(content_data).execute()
            content_result = self._handle_response(response)

            if content_result and len(content_result) > 0:
                content_id = content_result[0]['id']
                logger.debug(f"Created new email content with ID: {content_id}")
            else:
                logger.error("Failed to create email content")
                return None

        # Now create the email queue entry
        email_data = {
            'account_id': account_id,
            'recipient_id': recipient_id,
            'content_id': content_id,
            'scheduled_time': scheduled_time.isoformat(),
            'status': 'scheduled'
        }

        response = self.client.table('email_queue').insert(email_data).execute()
        result = self._handle_response(response)

        # If successful, add subject and body to the result for backward compatibility
        if result and len(result) > 0:
            result[0]['subject'] = subject
            result[0]['body'] = body

        return result

    def get_emails_to_send(self, time_window_minutes: int = 5) -> List[Dict[str, Any]]:
        """
        Get emails that need to be sent in the next X minutes.

        Args:
            time_window_minutes (int, optional): Time window in minutes

        Returns:
            List[Dict[str, Any]]: List of emails to send
        """
        logger.info(f"Calling get_emails_to_send with time_window_minutes={time_window_minutes}")

        try:
            # Calculate the time window
            now = datetime.datetime.now(datetime.timezone.utc)
            end_time = now + datetime.timedelta(minutes=time_window_minutes)

            # Get all scheduled emails
            response = self.client.table('email_queue').select('*').eq('status', 'scheduled').lte('scheduled_time', end_time.isoformat()).execute()
            emails = self._handle_response(response)

            if not emails:
                logger.info("No scheduled emails found")
                return []

            # Process each email to get the required data
            result = []
            for email in emails:
                email_id = email.get('id')
                account_id = email.get('account_id')
                recipient_id = email.get('recipient_id')
                content_id = email.get('content_id')

                # Get recipient data
                recipient_response = self.client.table('recipients').select('email').eq('id', recipient_id).execute()
                recipient_data = self._handle_response(recipient_response)
                recipient_email = recipient_data[0].get('email') if recipient_data and len(recipient_data) > 0 else None

                # Get account data
                account_response = self.client.table('email_accounts').select('email', 'smtp_server', 'smtp_port', 'use_ssl').eq('id', account_id).execute()
                account_data = self._handle_response(account_response)
                sender_email = account_data[0].get('email') if account_data and len(account_data) > 0 else None
                smtp_server = account_data[0].get('smtp_server') if account_data and len(account_data) > 0 else None
                smtp_port = account_data[0].get('smtp_port') if account_data and len(account_data) > 0 else None
                use_ssl = account_data[0].get('use_ssl') if account_data and len(account_data) > 0 else None

                # Get content data
                content_response = self.client.table('email_content').select('subject', 'body').eq('id', content_id).execute()
                content_data = self._handle_response(content_response)
                subject = content_data[0].get('subject') if content_data and len(content_data) > 0 else None
                body = content_data[0].get('body') if content_data and len(content_data) > 0 else None

                # Create the processed email
                processed_email = {
                    'id': email_id,
                    'account_id': account_id,
                    'recipient_id': recipient_id,
                    'recipient_email': recipient_email,
                    'subject': subject,
                    'body': body,
                    'tracking_id': email.get('tracking_id'),
                    'scheduled_time': email.get('scheduled_time'),
                    'sender_email': sender_email,
                    'smtp_server': smtp_server,
                    'smtp_port': smtp_port,
                    'use_ssl': use_ssl,
                    'is_follow_up': email.get('is_follow_up'),
                    'follow_up': email.get('follow_up'),
                    'previous_email_id': email.get('previous_email_id'),
                    'content_id': content_id
                }

                result.append(processed_email)

            logger.info(f"get_emails_to_send returned {len(result)} emails")
            # Log the first few emails for debugging
            for i, email in enumerate(result[:3]):  # Log up to 3 emails
                scheduled_time = email.get('scheduled_time')
                subject = email.get('subject')
                recipient = email.get('recipient_email')
                logger.info(f"  Email {i+1}: ID={email.get('id')}, To={recipient}, Subject={subject}, Scheduled={scheduled_time}")

            return result

        except Exception as e:
            logger.error(f"Error in get_emails_to_send: {str(e)}")
            return []

    def update_email_status(self, email_id: str, status: str,
                           error_message: str = None) -> Dict[str, Any]:
        """
        Update the status of an email in the queue.

        Args:
            email_id (str): Email ID
            status (str): New status
            error_message (str, optional): Error message if status is 'failed'

        Returns:
            Dict[str, Any]: Updated email
        """
        update_data = {
            'status': status,
            'updated_at': datetime.datetime.now().isoformat()
        }

        if status == 'sending':
            # First get the current attempts count
            current_email = self.get_email_by_id(email_id)
            if current_email:
                current_attempts = current_email.get('attempts', 0)
                update_data['attempts'] = current_attempts + 1
            else:
                update_data['attempts'] = 1

            update_data['last_attempt_time'] = datetime.datetime.now().isoformat()
        elif status == 'sent':
            update_data['sent_time'] = datetime.datetime.now().isoformat()
        elif status == 'failed' and error_message:
            update_data['error_message'] = error_message

        response = self.client.table('email_queue').update(update_data).eq('id', email_id).execute()
        return self._handle_response(response)

    def get_email_by_id(self, email_id: str) -> Dict[str, Any]:
        """
        Get an email by its ID.

        Args:
            email_id (str): Email ID

        Returns:
            Dict[str, Any]: Email data or None if not found
        """
        try:
            # Get the email from the queue
            response = self.client.table('email_queue').select('*').eq('id', email_id).execute()
            result = self._handle_response(response)

            if not result or len(result) == 0:
                return None

            email = result[0]

            # Get the subject and body from the email_content table
            if email.get('content_id'):
                content_response = self.client.table('email_content').select('subject,body').eq('id', email['content_id']).execute()
                content_result = self._handle_response(content_response)

                if content_result and len(content_result) > 0:
                    # Add subject and body to the email data
                    email['subject'] = content_result[0]['subject']
                    email['body'] = content_result[0]['body']

            return email

        except Exception as e:
            logger.error(f"Error in get_email_by_id: {str(e)}")
            return None

    def update_email(self, email_id: str, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update an email in the queue with custom data.

        Args:
            email_id (str): Email ID
            update_data (Dict[str, Any]): Data to update

        Returns:
            Dict[str, Any]: Updated email
        """
        # Add updated_at timestamp
        update_data['updated_at'] = datetime.datetime.now().isoformat()

        response = self.client.table('email_queue').update(update_data).eq('id', email_id).execute()
        return self._handle_response(response)

    def get_email_content(self, email_id: str) -> Dict[str, Any]:
        """
        Get the subject and body for an email.

        Args:
            email_id (str): Email ID

        Returns:
            Dict[str, Any]: Email content with subject and body
        """
        try:
            # Call the database function to get email content
            response = self.client.rpc(
                'get_email_content',
                {'p_email_id': email_id}
            ).execute()

            result = self._handle_response(response)

            if result and len(result) > 0:
                return result[0]
            else:
                # Fallback: try to get content_id from email_queue and then get content
                email = self.get_email_by_id(email_id)
                if email and email.get('content_id'):
                    content_id = email.get('content_id')
                    response = self.client.table('email_content').select('*').eq('id', content_id).execute()
                    content = self._handle_response(response)

                    if content and len(content) > 0:
                        return content[0]

            return None

        except Exception as e:
            logger.error(f"Error getting email content for {email_id}: {str(e)}")
            return None

    def get_email_content(self, email_id: str) -> Dict[str, Any]:
        """
        Get the subject and body for an email.

        Args:
            email_id (str): Email ID

        Returns:
            Dict[str, Any]: Email content with subject and body
        """
        try:
            # Call the database function to get email content
            response = self.client.rpc(
                'get_email_content',
                {'p_email_id': email_id}
            ).execute()

            result = self._handle_response(response)

            if result and len(result) > 0:
                return result[0]
            else:
                # Fallback: try to get content_id from email_queue and then get content
                email = self.get_email_by_id(email_id)
                if email and email.get('content_id'):
                    content_id = email.get('content_id')
                    response = self.client.table('email_content').select('*').eq('id', content_id).execute()
                    content = self._handle_response(response)

                    if content and len(content) > 0:
                        return content[0]

            return None

        except Exception as e:
            logger.error(f"Error getting email content for {email_id}: {str(e)}")
            return None

    def get_template_id(self, follow_up: int) -> str:
        """
        Get the appropriate template ID based on follow-up number.

        Args:
            follow_up (int): Follow-up number (0-4)

        Returns:
            str: Template ID
        """
        # Map follow_up number to template ID
        if follow_up == 0:
            return '00000000-0000-0000-0000-000000000001'  # Initial email
        elif follow_up == 1:
            return '00000000-0000-0000-0000-000000000002'  # 1st follow-up
        elif follow_up == 2:
            return '00000000-0000-0000-0000-000000000003'  # 2nd follow-up
        elif follow_up == 3:
            return '00000000-0000-0000-0000-000000000004'  # 3rd follow-up
        elif follow_up == 4:
            return '00000000-0000-0000-0000-000000000005'  # 4th follow-up (final)
        else:
            # Default to initial email template for unexpected follow_up values
            logger.warning(f"Unexpected follow_up value: {follow_up}, using initial email template")
            return '00000000-0000-0000-0000-000000000001'

    def get_email_content(self, email_id: str) -> Dict[str, Any]:
        """
        Get the subject and body for an email.

        Args:
            email_id (str): Email ID

        Returns:
            Dict[str, Any]: Email content with subject and body
        """
        try:
            # Call the database function to get email content
            response = self.client.rpc(
                'get_email_content',
                {'p_email_id': email_id}
            ).execute()

            result = self._handle_response(response)

            if result and len(result) > 0:
                return result[0]
            else:
                # Fallback: try to get content_id from email_queue and then get content
                email = self.get_email_by_id(email_id)
                if email and email.get('content_id'):
                    content_id = email.get('content_id')
                    response = self.client.table('email_content').select('*').eq('id', content_id).execute()
                    content = self._handle_response(response)

                    if content and len(content) > 0:
                        return content[0]

            return None

        except Exception as e:
            logger.error(f"Error getting email content for {email_id}: {str(e)}")
            return None

    # Email Tracking

    def create_email_tracking_record(self, email_id: str, tracking_id: str, recipient_id: str) -> Dict[str, Any]:
        """
        Create an initial tracking record for a sent email.

        Args:
            email_id (str): Email ID
            tracking_id (str): Email tracking ID
            recipient_id (str): Recipient ID

        Returns:
            Dict[str, Any]: Created tracking record or None if failed
        """
        try:
            logger.info(f"Creating initial tracking record for email ID: {email_id}, tracking ID: {tracking_id}")

            # Check if a tracking record already exists
            existing_response = self.client.table('email_tracking').select('id').eq('tracking_id', tracking_id).execute()
            existing = self._handle_response(existing_response)

            if existing and len(existing) > 0:
                logger.info(f"Tracking record already exists for tracking ID: {tracking_id}")
                return existing[0]

            # Create initial tracking data with NULL open_time (will be updated when email is opened)
            tracking_data = {
                'email_id': email_id,
                'tracking_id': tracking_id,
                'recipient_id': recipient_id,
                # No open_time set initially - it will be NULL in the database
                # Other fields will also be NULL until the email is opened
            }

            # Insert tracking data into the email_tracking table
            logger.info(f"Inserting initial tracking data for email ID: {email_id}")
            response = self.client.table('email_tracking').insert(tracking_data).execute()
            result = self._handle_response(response)

            if result and len(result) > 0:
                logger.info(f"Successfully created initial tracking record with ID: {result[0].get('id')}")
                return result[0]
            else:
                logger.error("Failed to insert initial tracking data into Supabase")
                return None

        except Exception as e:
            logger.error(f"Error creating initial tracking record: {str(e)}")
            return None

    def record_email_open(self, tracking_id: str, user_agent: str = None,
                         ip_address: str = None) -> Dict[str, Any]:
        """
        Record an email open event.

        Args:
            tracking_id (str): Email tracking ID
            user_agent (str, optional): User agent
            ip_address (str, optional): IP address

        Returns:
            Dict[str, Any]: Tracking record or None if failed
        """
        try:
            logger.info(f"Recording email open in Supabase for tracking ID: {tracking_id}")

            # Check if a tracking record already exists
            tracking_response = self.client.table('email_tracking').select('*').eq('tracking_id', tracking_id).execute()
            tracking_record = self._handle_response(tracking_response)

            if not tracking_record or len(tracking_record) == 0:
                # No existing record found, get email details to create one
                email_response = self.client.table('email_queue').select('id, recipient_id').eq('tracking_id', tracking_id).execute()
                email = self._handle_response(email_response)

                if not email or len(email) == 0:
                    logger.error(f"No email found with tracking ID: {tracking_id}")
                    return None

                email_id = email[0]['id']
                recipient_id = email[0]['recipient_id']

                logger.info(f"No existing tracking record found. Creating new record for email ID: {email_id}")

                # Create new tracking data
                tracking_data = {
                    'email_id': email_id,
                    'tracking_id': tracking_id,
                    'recipient_id': recipient_id,
                    'open_time': datetime.datetime.now().isoformat(),
                    'user_agent': user_agent,
                    'ip_address': ip_address
                }

                # Parse user agent to extract device, browser, OS
                if user_agent:
                    # Simple parsing logic - in a real app, use a proper user agent parser
                    tracking_data['device_type'] = 'mobile' if 'Mobile' in user_agent else 'desktop'

                    if 'Chrome' in user_agent:
                        tracking_data['browser'] = 'Chrome'
                    elif 'Firefox' in user_agent:
                        tracking_data['browser'] = 'Firefox'
                    elif 'Safari' in user_agent:
                        tracking_data['browser'] = 'Safari'
                    elif 'Edge' in user_agent:
                        tracking_data['browser'] = 'Edge'

                    if 'Windows' in user_agent:
                        tracking_data['os'] = 'Windows'
                    elif 'Mac' in user_agent:
                        tracking_data['os'] = 'macOS'
                    elif 'Android' in user_agent:
                        tracking_data['os'] = 'Android'
                    elif 'iOS' in user_agent:
                        tracking_data['os'] = 'iOS'

                # Insert new tracking record
                logger.info(f"Inserting new tracking data for email ID: {email_id}")
                response = self.client.table('email_tracking').insert(tracking_data).execute()
                result = self._handle_response(response)
            else:
                # Existing record found, update it
                tracking_id_db = tracking_record[0]['id']
                email_id = tracking_record[0]['email_id']

                logger.info(f"Found existing tracking record with ID: {tracking_id_db} for email ID: {email_id}")

                # Prepare update data
                update_data = {
                    'open_time': datetime.datetime.now().isoformat(),
                    'user_agent': user_agent,
                    'ip_address': ip_address
                }

                # Parse user agent to extract device, browser, OS
                if user_agent:
                    # Simple parsing logic - in a real app, use a proper user agent parser
                    update_data['device_type'] = 'mobile' if 'Mobile' in user_agent else 'desktop'

                    if 'Chrome' in user_agent:
                        update_data['browser'] = 'Chrome'
                    elif 'Firefox' in user_agent:
                        update_data['browser'] = 'Firefox'
                    elif 'Safari' in user_agent:
                        update_data['browser'] = 'Safari'
                    elif 'Edge' in user_agent:
                        update_data['browser'] = 'Edge'

                    if 'Windows' in user_agent:
                        update_data['os'] = 'Windows'
                    elif 'Mac' in user_agent:
                        update_data['os'] = 'macOS'
                    elif 'Android' in user_agent:
                        update_data['os'] = 'Android'
                    elif 'iOS' in user_agent:
                        update_data['os'] = 'iOS'

                # Update existing tracking record
                logger.info(f"Updating existing tracking record with ID: {tracking_id_db}")
                response = self.client.table('email_tracking').update(update_data).eq('id', tracking_id_db).execute()
                result = self._handle_response(response)

            if result and len(result) > 0:
                logger.info(f"Successfully recorded email open in Supabase with tracking record ID: {result[0].get('id')}")
                return result[0]
            else:
                logger.error("Failed to insert tracking data into Supabase")
                return None

        except Exception as e:
            logger.error(f"Error recording email open in Supabase: {str(e)}")
            return None

    def get_email_tracking_data(self, tracking_id: str = None, email_id: str = None) -> List[Dict[str, Any]]:
        """
        Get tracking data for an email.

        Args:
            tracking_id (str, optional): Email tracking ID
            email_id (str, optional): Email ID

        Returns:
            List[Dict[str, Any]]: List of tracking records
        """
        try:
            query = self.client.table('email_tracking').select('*')

            if tracking_id:
                query = query.eq('tracking_id', tracking_id)
            elif email_id:
                query = query.eq('email_id', email_id)

            response = query.execute()
            result = self._handle_response(response)

            if result:
                logger.info(f"Retrieved {len(result)} tracking records")
                return result
            else:
                logger.info("No tracking records found")
                return []

        except Exception as e:
            logger.error(f"Error retrieving tracking data: {str(e)}")
            return []

    def get_tracking_summary(self) -> Dict[str, Any]:
        """
        Get a summary of all email tracking data.

        Returns:
            Dict[str, Any]: Summary of tracking data
        """
        try:
            # Get total number of emails sent
            emails_response = self.client.table('email_queue').select('id').execute()
            emails = self._handle_response(emails_response)
            total_emails = len(emails) if emails else 0

            # Get total number of email opens
            tracking_response = self.client.table('email_tracking').select('id').execute()
            tracking = self._handle_response(tracking_response)
            total_opens = len(tracking) if tracking else 0

            # Get unique emails opened
            if tracking and len(tracking) > 0:
                unique_emails_response = self.client.table('email_tracking').select('email_id').execute()
                unique_emails = self._handle_response(unique_emails_response)
                unique_email_ids = set(record['email_id'] for record in unique_emails) if unique_emails else set()
                unique_opens = len(unique_email_ids)
            else:
                unique_opens = 0

            # Calculate open rate
            open_rate = (unique_opens / total_emails) * 100 if total_emails > 0 else 0

            summary = {
                "total_emails_sent": total_emails,
                "total_opens": total_opens,
                "unique_emails_opened": unique_opens,
                "open_rate": f"{open_rate:.2f}%"
            }

            logger.info(f"Generated tracking summary: {summary}")
            return summary

        except Exception as e:
            logger.error(f"Error generating tracking summary: {str(e)}")
            return {
                "total_emails_sent": 0,
                "total_opens": 0,
                "unique_emails_opened": 0,
                "open_rate": "0.00%",
                "error": str(e)
            }

    # Email Scheduling

    def generate_random_time(self, date: datetime.date = None) -> datetime.datetime:
        """
        Generate a random time between 9am and 5pm for the given date.

        Args:
            date (datetime.date, optional): The date to generate a time for. Defaults to today.

        Returns:
            datetime.datetime: A random datetime between 9am and 5pm
        """
        if date is None:
            date = datetime.date.today()

        # Generate random time between 9am and 5pm
        business_start = datetime.datetime.combine(date, datetime.time(9, 0))
        business_end = datetime.datetime.combine(date, datetime.time(17, 0))
        total_seconds = (business_end - business_start).total_seconds()

        import random
        random_seconds = random.randint(0, int(total_seconds))
        return business_start + datetime.timedelta(seconds=random_seconds)

    def schedule_emails_for_account(self, account_id: str, recipient_ids: List[str],
                                   subject: str, body: str,
                                   date: datetime.date = None,
                                   num_emails: int = None) -> List[Dict[str, Any]]:
        """
        Schedule multiple emails for an account on a given date at random times.

        Args:
            account_id (str): Email account ID
            recipient_ids (List[str]): List of recipient IDs
            subject (str): Email subject
            body (str): Email body
            date (datetime.date, optional): Date to schedule emails for. Defaults to today.
            num_emails (int, optional): Number of emails to schedule. Defaults to account's daily limit.

        Returns:
            List[Dict[str, Any]]: List of scheduled emails
        """
        if date is None:
            date = datetime.date.today()

        # Get daily limit if num_emails not specified
        if num_emails is None:
            account_response = self.client.table('email_accounts').select('daily_limit').eq('id', account_id).execute()
            account = self._handle_response(account_response)
            num_emails = account[0]['daily_limit'] if account and len(account) > 0 else 10

        # Limit number of emails to number of recipients
        num_emails = min(num_emails, len(recipient_ids))

        # Schedule emails
        scheduled_emails = []
        for i in range(num_emails):
            # Generate random time
            scheduled_time = self.generate_random_time(date)

            # Queue the email
            email = self.queue_email(
                account_id=account_id,
                recipient_id=recipient_ids[i % len(recipient_ids)],  # Cycle through recipients
                subject=subject,
                body=body,
                scheduled_time=scheduled_time
            )

            if email:
                scheduled_emails.append(email)

        return scheduled_emails

    # Follow-up Emails

    def schedule_follow_up(self, email_id: str, follow_up_days: int) -> Dict[str, Any]:
        """
        Schedule a follow-up email for a sent email.

        Args:
            email_id (str): ID of the original email
            follow_up_days (int): Number of days to wait before sending the follow-up

        Returns:
            Dict[str, Any]: The scheduled follow-up email
        """
        # Get the original email to determine the follow-up number
        original_email = self.get_email_by_id(email_id)
        if not original_email:
            logger.error(f"Original email with ID {email_id} not found")
            return None

        # Calculate the follow-up number (original email follow_up + 1)
        current_follow_up = original_email.get('follow_up', 0)
        next_follow_up = current_follow_up + 1

        # Check if we've reached the maximum follow-up number (4)
        if next_follow_up > 4:
            logger.warning(f"Maximum follow-up number reached for email {email_id}")
            return None

        # Get the appropriate template ID for this follow-up
        template_id = self.get_template_id(next_follow_up)

        # Call the database function to schedule the follow-up
        params = {
            'email_id': email_id,
            'follow_up_days': follow_up_days
        }

        response = self.client.rpc('schedule_follow_up_email', params).execute()
        result = self._handle_response(response)

        if result and len(result) > 0:
            # Update the content_id to use the appropriate template
            follow_up_id = result[0].get('id')
            if follow_up_id:
                self.update_email(follow_up_id, {
                    'content_id': template_id,
                    'follow_up': next_follow_up
                })

                # Get the updated email with template content
                return self.get_email_by_id(follow_up_id)

        return result

# Create a singleton instance
db = SupabaseClient()
